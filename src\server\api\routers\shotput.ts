import { and, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { ShotputHand, ShotputMovement, ShotputType } from "~/lib/enums/shotput";
import type { TaglistTag } from "~/lib/interface";
import { shotputTags, shotputThrows } from "~/server/db/schema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";

type Outputs = RouterOutputs["shotput"];

export type GetShotputTagsOutput = Outputs["getTags"];

export const shotputRouter = createTRPCRouter({
  getTags: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const throws = await ctx.db.query.shotputThrows.findMany({
        where: eq(shotputThrows.videoId, input.id),
        with: {
          phases: {
            where: eq(shotputTags.isDeleted, false),
          },
        },
      });

      const formatTags: TaglistTag[] = [];
      throws.forEach((t) => {
        t.phases
          .filter((p) => !p.isDeleted)
          .forEach((p) => {
            formatTags.push({
              id: p.id.toString(),
              throwId: p.throwId ?? undefined,
              throwNumber: t.number,
              videoId: t.videoId,
              userId: p.userId,
              frame: p.frame,
              aiFrame: p.aiFrame,
              tag: p.tag,
              athleteId: t.athleteId,
              isDeleted: p.isDeleted,
            });
          });
      });

      return {
        tags: formatTags,
        throws: throws.map((x) => ({ ...x, phases: undefined })),
      };
    }),

  editTagAthlete: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
        originalAthleteId: z.string(),
        newAthleteId: z.string(),
        number: z.number(),
      }),
    )
    .mutation(({ input, ctx }) => {
      return ctx.db
        .update(shotputThrows)
        .set({ athleteId: input.newAthleteId })
        .where(
          and(
            eq(shotputThrows.videoId, input.videoId),
            eq(shotputThrows.athleteId, input.originalAthleteId),
            eq(shotputThrows.number, input.number),
          ),
        );
    }),
  deleteAthleteTags: protectedProcedure
    .input(z.object({ athleteId: z.string(), videoId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      await ctx.db.transaction(async (tx) => {
        // First get all throw IDs for this athlete and video
        const throws = await tx
          .select({ id: shotputThrows.id })
          .from(shotputThrows)
          .where(
            and(
              eq(shotputThrows.athleteId, input.athleteId),
              eq(shotputThrows.videoId, input.videoId),
            ),
          );

        // Delete all tags associated with these throws
        if (throws.length > 0) {
          await tx.delete(shotputTags).where(
            inArray(
              shotputTags.throwId,
              throws.map((t) => t.id),
            ),
          );
        }

        // Finally delete the throws
        await tx
          .delete(shotputThrows)
          .where(
            and(
              eq(shotputThrows.athleteId, input.athleteId),
              eq(shotputThrows.videoId, input.videoId),
            ),
          );
      });
    }),
  getThrows: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.query.shotputThrows.findMany({
        where: (throws, { eq }) => eq(throws.videoId, input.videoId),
        orderBy: (throws, { asc }) => [asc(throws.number)],
      });
    }),
  /**
   * only use for tagger
   */
  upsertShotputThrow: protectedProcedure
    .input(
      z.object({
        id: z.number().optional(),
        videoId: z.string(),
        athleteId: z.string(),
        number: z.number(),
        movement: z.nativeEnum(ShotputMovement),
        type: z.nativeEnum(ShotputType),
        hand: z.nativeEnum(ShotputHand),
        weight: z.number(),
        comment: z.string(),
        isToolUsed: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const obj = {
        id: input.id,
        videoId: input.videoId,
        athleteId: input.athleteId,
        number: Number(input.number),
        type: input.type,
        movement: input.movement,
        hand: input.hand,
        userId: ctx.session.user.id,
        distance: null,
        dateCreated: new Date(),
        weight: input.weight,
        comment: input.comment,
        isToolUsed: input.isToolUsed ?? false,
      };
      const id = await ctx.db
        .insert(shotputThrows)
        .values(obj)
        .onDuplicateKeyUpdate({
          set: obj,
        })
        .$returningId();

      return { ...obj, id: input.id ?? id[0]!.id };
    }),

  upsertTag: protectedProcedure
    .input(
      z.object({
        id: z.number().optional(),
        throwId: z.number(),
        tag: z.string(),
        frame: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(shotputTags)
        .values({
          ...input,
          userId: ctx.session.user.id,
        })
        .onDuplicateKeyUpdate({
          set: {
            frame: input.frame,
            userId: ctx.session.user.id,
            isDeleted: false,
          },
        })
        .$returningId();
    }),

  deleteTag: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(shotputTags)
        .set({
          isDeleted: true,
        })
        .where(eq(shotputTags.id, input.id));
    }),

  deleteTags: protectedProcedure
    .input(z.object({ ids: z.array(z.number()) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(shotputTags)
        .set({ isDeleted: true })
        .where(inArray(shotputTags.id, input.ids));
    }),

  // finishReview: protectedProcedure
  //   .input(
  //     z.object({
  //       videoId: z.string(),
  //       changes: z
  //         .array(
  //           z.object({
  //             id: z.string(),
  //             action: z.nativeEnum(TagAction),
  //             athleteId: z.string(),
  //             frame: z.number(),
  //             tag: z.string(),
  //             throwId: z.number(),
  //           }),
  //         )
  //         .optional(),
  //     }),
  //   )
  //   .mutation(async ({ ctx, input }) => {
  //     await ctx.db.transaction(async (tx) => {
  //       //delete tags
  //       const tagsToDelete = input.changes
  //         ?.filter((x) => x.action === TagAction.remove)
  //         .map((change) => change.id);

  //       if (tagsToDelete?.length) {
  //         await tx.delete(shotputTags).where(
  //           inArray(
  //             shotputTags.id,
  //             tagsToDelete.map((id) => Number(id)),
  //           ),
  //         );
  //       }

  //       //update tags
  //       const phasesToUpdate = input.changes?.filter(
  //         (x) => x.action === TagAction.update,
  //       );

  //       if (phasesToUpdate && phasesToUpdate.length > 0) {
  //         const sqlChunksFrame: SQL[] = [sql`(case`];
  //         const sqlChunksPhase: SQL[] = [sql`(case`];
  //         const ids: string[] = [];

  //         for (const phase of phasesToUpdate) {
  //           sqlChunksFrame.push(
  //             sql`when ${shotputTags.id} = ${phase.id} then ${phase.frame}`,
  //           );
  //           sqlChunksPhase.push(
  //             sql`when ${shotputTags.id} = ${phase.id} then ${phase.tag}`,
  //           );
  //           ids.push(phase.id);
  //         }

  //         sqlChunksFrame.push(sql`end)`);
  //         sqlChunksPhase.push(sql`end)`);

  //         await tx
  //           .update(shotputTags)
  //           .set({
  //             frame: sql.join(sqlChunksFrame, sql.raw(" ")),
  //             tag: sql.join(sqlChunksPhase, sql.raw(" ")),
  //           })
  //           .where(
  //             inArray(
  //               shotputTags.id,
  //               ids.map((id) => Number(id)),
  //             ),
  //           );
  //       }

  //       //insert tags
  //       const phasesToAdd = input.changes?.filter(
  //         (x) => x.action === TagAction.add,
  //       );
  //       if (phasesToAdd && phasesToAdd.length > 0) {
  //         await tx.insert(shotputTags).values(
  //           phasesToAdd.map((phase) => ({
  //             throwId: phase.throwId,
  //             tag: phase.tag,
  //             frame: phase.frame,
  //             userId: ctx.session.user.id,
  //           })),
  //         );
  //       }

  //       await updateVideoStatus(input.videoId);
  //     });

  //     const aiTaggedVideos = await getVideos({
  //       token: ctx.session.accessToken,
  //       sport: [Sport.athletics],
  //     });
  //     return aiTaggedVideos.list[0];
  //   }),
});
